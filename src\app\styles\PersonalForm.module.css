/* Enhanced page wrapper with modern background */
.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-image: url('/assets/pagewormu.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow-x: hidden;
}

.page {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120px 20px 40px;
  min-height: calc(100vh - 160px);
}

/* Modern form container with enhanced styling */
.formContainer {
  background: rgba(0, 0, 0, 0.85);
  padding: 40px;
  border-radius: 24px;
  width: 100%;
  max-width: 480px;
  color: white;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 184, 51, 0.2);
  backdrop-filter: blur(15px);
  animation: slideInUp 0.8s ease-out;
  position: relative;
  overflow: visible;
}

/* Subtle gradient overlay for premium feel */
.formContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary, #e98e0f), var(--primary-light, #FFB833), transparent);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced header section */
.formTitle {
  text-align: center;
  font-size: 36px;
  font-weight: 700;
  font-family: var(--font-montserrat, 'Montserrat'), sans-serif;
  margin-bottom: 12px;
  color: #FFB833;
  letter-spacing: 0.5px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  padding-bottom: 20px;
}

/* Decorative underline for title */
.formTitle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary, #e98e0f), transparent);
  border-radius: 2px;
}

.formSubtitle {
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  margin-bottom: 35px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* Enhanced input styles */
.input {
  width: 100%;
  padding: 16px 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  border: 2px solid transparent;
  outline: none;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
}

.input:focus {
  border-color: var(--primary, #e98e0f);
  box-shadow: 0 0 0 4px rgba(233, 142, 15, 0.2), 0 6px 20px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

.input::placeholder {
  color: #888;
  font-weight: 400;
}

.inputGroup {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  align-items: stretch;
  position: relative;
  z-index: 1;
}

.unitSelect {
  width: 80px;
  flex-shrink: 0;
  padding: 16px 12px;
  border-radius: 12px;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  height: 56px;
  line-height: normal;
  box-sizing: border-box;
}

.unitSelect:focus {
  border-color: var(--primary, #e98e0f);
  box-shadow: 0 0 0 4px rgba(233, 142, 15, 0.2);
  outline: none;
  position: relative;
  z-index: 10;
}

.dropdown {
  width: 100%;
  padding: 16px 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.dropdown:focus {
  border-color: var(--primary, #e98e0f);
  box-shadow: 0 0 0 4px rgba(233, 142, 15, 0.2), 0 6px 20px rgba(0, 0, 0, 0.15);
  outline: none;
  transform: translateY(-1px);
  position: relative;
  z-index: 10;
}

/* Enhanced goal section with modern radio buttons */
.goalSection {
  margin-bottom: 30px;
}

.goalLabel {
  display: block;
  font-weight: 600;
  margin-bottom: 16px;
  color: #FFB833;
  font-size: 18px;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  letter-spacing: 0.3px;
}

.goalOptions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.goalOptions label {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-weight: 500;
  font-size: 14px;
  min-width: 100px;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.goalOptions label:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.goalOptions input[type='radio'] {
  display: none;
}

.goalOptions input[type='radio']:checked + span {
  background: linear-gradient(135deg, var(--primary, #e98e0f), var(--primary-light, #FFB833));
  color: white;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.goalOptions label:has(input[type='radio']:checked) {
  background: linear-gradient(135deg, var(--primary, #e98e0f), var(--primary-light, #FFB833));
  border-color: var(--primary-light, #FFB833);
  color: white;
  box-shadow: 0 6px 20px rgba(233, 142, 15, 0.4);
  transform: translateY(-2px);
}

/* Enhanced submit button */
.nextBtn {
  width: 100%;
  background: linear-gradient(135deg, var(--primary, #e98e0f), var(--primary-light, #FFB833));
  padding: 18px 24px;
  border: none;
  font-weight: 700;
  color: white;
  border-radius: 12px;
  cursor: pointer;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(233, 142, 15, 0.3);
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}

.nextBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-light, #FFB833), var(--primary, #e98e0f));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nextBtn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(233, 142, 15, 0.4);
}

.nextBtn:hover::before {
  opacity: 1;
}

.nextBtn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(233, 142, 15, 0.3);
}

.nextBtn:disabled {
  background: linear-gradient(135deg, #999, #777);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Enhanced activity level tooltip styles */
.selectWithTooltip {
  position: relative;
  margin-bottom: 20px;
}

.tooltipText {
  background: linear-gradient(135deg, rgba(233, 142, 15, 0.95), rgba(255, 184, 51, 0.95));
  color: white;
  font-size: 14px;
  padding: 12px 16px;
  border-radius: 10px;
  margin-top: 8px;
  font-weight: 500;
  animation: slideInTooltip 0.3s ease-out;
  box-shadow: 0 4px 15px rgba(233, 142, 15, 0.3);
  border: 1px solid rgba(255, 184, 51, 0.3);
  backdrop-filter: blur(5px);
  line-height: 1.4;
}

/* Enhanced feet and inches input styles */
.feetInchInputs {
  display: flex;
  flex: 1;
  gap: 8px;
  max-width: calc(100% - 92px); /* Reserve space for unit selector */
}

.feetInput, .inchesInput {
  flex: 1;
  padding: 16px 12px;
  border: 2px solid transparent;
  border-radius: 12px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  min-width: 0; /* Allow flex items to shrink */
}

.feetInput:focus, .inchesInput:focus {
  border-color: var(--primary, #e98e0f);
  box-shadow: 0 0 0 4px rgba(233, 142, 15, 0.2), 0 6px 20px rgba(0, 0, 0, 0.15);
  outline: none;
  transform: translateY(-1px);
}

.feetInput::placeholder, .inchesInput::placeholder {
  color: #888;
  font-weight: 400;
}

/* Enhanced animations */
@keyframes slideInTooltip {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form field labels */
.fieldLabel {
  display: block;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 14px;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  letter-spacing: 0.3px;
}

/* Responsive design */
@media (max-width: 480px) {
  .formContainer {
    padding: 30px 20px;
    margin: 20px;
  }

  .formTitle {
    font-size: 28px;
  }

  .goalOptions {
    flex-direction: column;
    gap: 8px;
  }

  .goalOptions label {
    min-width: auto;
  }
}
