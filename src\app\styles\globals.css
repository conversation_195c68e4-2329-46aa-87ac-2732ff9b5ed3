/* Color System */
:root {
  /* Primary Colors */
  --primary: #e98e0f;
  --primary-light: #FFB833;
  --primary-dark: #d17400;

  /* Secondary Colors */
  --secondary: #47CF73;
  --secondary-light: #6DF598;
  --secondary-dark: #2A9B4F;

  /* Accent Colors */
  --accent: #D43BF6;
  --accent-light: #E87AFF;
  --accent-dark: #A01BC3;

  /* Neutral Colors */
  --dark-bg: rgba(0, 0, 0, 0.85);
  --card-bg: rgba(20, 20, 20, 0.9);
  --light-text: #f0f0f0;
  --muted-text: #b0b0b0;

  /* Feedback Colors */
  --success: #47CF73;
  --error: #FF6B6B;
  --warning: #FFD93D;
  --info: #5BC0EB;

  /* UI Elements */
  --border-radius-sm: 6px;
  --border-radius-md: 10px;
  --border-radius-lg: 16px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  --box-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.4);
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Reset & base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  background-color: #000;
  color: var(--light-text);
  /* Fix scroll behavior */
  overflow-x: hidden; /* Prevent horizontal scrolling */
  scroll-behavior: smooth; /* Smooth scrolling */
  /* Prevent bounce/rubber-band effect */
  overscroll-behavior: none;
  overscroll-behavior-x: none;
  overscroll-behavior-y: none;
  /* Additional mobile/touch device fixes */
  -webkit-overflow-scrolling: touch;
  /* Prevent pull-to-refresh on mobile */
  touch-action: pan-y;
}

/* Ensure content doesn't get hidden behind navbar */
body {
  padding-top: 0; /* Reset any default padding */
}

/* Utility classes (optional & reusable) */
.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Common Component Styles */

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  border: none;
  outline: none;
  text-decoration: none;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  box-shadow: 0 2px 10px rgba(233, 142, 15, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(233, 142, 15, 0.5);
}

.btn-primary:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(233, 142, 15, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
  color: white;
  box-shadow: 0 2px 10px rgba(71, 207, 115, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(71, 207, 115, 0.5);
}

.btn-danger {
  background: linear-gradient(135deg, #e53935, #ff6b6b);
  color: white;
  box-shadow: 0 2px 10px rgba(229, 57, 53, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(229, 57, 53, 0.5);
}

/* Card Styles */
.card {
  background: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: 25px;
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(255, 184, 51, 0.1);
  transition: var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-hover);
}

/* Form Element Styles */
.input-field {
  width: 100%;
  padding: 12px 16px;
  border-radius: var(--border-radius-md);
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  transition: var(--transition-normal);
}

.input-field:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(233, 142, 15, 0.2);
  outline: none;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}
