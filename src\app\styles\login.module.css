
.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 120vh;
  background-image: url('/assets/background_landing_signin.jpg');
  background-size: 100% 100%;

  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  overflow-x: hidden;
}

.page {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 100px;
}
 
/* Login form styles */
.loginForm {
  background: rgba(0, 0, 0, 0.75);
  padding: 30px;
  border-radius: 15px;
  width: 90%;
  max-width: 350px;
  color: #fff;
  margin-top: -50px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 184, 51, 0.1);
  backdrop-filter: blur(5px);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.formTitle {
  font-family: var(--font-montserrat, 'Montserrat'), sans-serif;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 25px;
  color: #FFB833;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  padding-bottom: 15px;
}

.formTitle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #e98e0f, transparent);
}

.errorMessage {
  background-color: rgba(255, 0, 0, 0.2);
  color: #ff6b6b;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
  border: 1px solid rgba(255, 0, 0, 0.3);
  font-size: 14px;
}

.inputField {
  width: 100%;
  padding: 10px 8px;
  margin-bottom: 20px;
  border-radius: 8px;
  border: 2px solid transparent;
  outline: none;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.inputField:focus {
  border-color: #e98e0f;
  box-shadow: 0 0 0 3px rgba(233, 142, 15, 0.2);
}

.passwordInput {
  position: relative;
}

.togglePassword {
  position: absolute;
  top: 30%;
  right: 10px;
  cursor: pointer;
}

.formOptions {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 15px;
}

.forgotLink {
  color: #e98e0f;
  text-decoration: none;
}

.loginBtn {
  width: 100%;
  padding: 12px 15px;
  background: linear-gradient(135deg, #e98e0f, #ffb733);
  color: #fff;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(233, 142, 15, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.loginBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ffb733, #e98e0f);
  z-index: -1;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.loginBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(233, 142, 15, 0.4);
}

.loginBtn:hover::before {
  opacity: 1;
}

.loginBtn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(233, 142, 15, 0.3);
}

.loginBtn:disabled {
  background: #999;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.signupText {
  font-size: 12px;
  text-align: center;
  margin-top: 15px;
}

.signupText a {
  color: #e98e0f;
  text-decoration: none;
}
