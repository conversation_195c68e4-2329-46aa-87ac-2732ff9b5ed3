.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-image: url('/assets/pagewormu.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow-x: hidden;
}

.pageContent {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.title {
  color: #FFD93D;
  font-size: 2rem;
  margin-bottom: 2rem;
  font-family: var(--font-montserrat, 'Montserrat'), sans-serif;
  text-align: center;
}

.loadingText {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: white;
}

.error {
  color: #ff6b6b;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 5px;
  margin: 1rem 0;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  margin: 2rem auto;
  max-width: 500px;
}

.browseLink {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.8rem 1.5rem;
  background-color: #e98e0f;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.browseLink:hover {
  background-color: #ffb733;
}

.categorySection {
  margin-bottom: 3rem;
}

.categoryTitle {
  color: #FFD93D;
  margin-bottom: 1rem;
  font-family: var(--font-montserrat, 'Montserrat'), sans-serif;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.card {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.videoWrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.clickOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}

.label {
  padding: 1rem;
  color: white;
  font-weight: 500;
  text-align: center;
  margin: 0;
}

.removeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s ease;
}

.removeButton:hover {
  background: rgba(255, 107, 107, 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pageContent {
    padding: 1rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}
