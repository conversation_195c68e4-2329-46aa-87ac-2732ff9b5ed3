.pageWrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-image: url('/assets/background_landing_signin.jpg');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    overflow-x: hidden;
  }
  
  .page {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
  }
  
  .confirmationBox {
    background: rgba(0, 0, 0, 0.7);
    padding: 30px 20px;
    border-radius: 10px;
    text-align: center;
    color: white;
    width: 90%;
    max-width: 400px;
    margin-bottom: 200px;
  }
  
  .title {
    font-size: 22px;
    font-weight: bold;
    font-family: Quicksand;
    margin-bottom: 10px;
  }
  
  .subtitle {
    font-size: 14px;
    font-weight: 500;
    font-family: Quicksand;
    margin-bottom: 20px;
  }
  
  .backBtn {
    width: 80%;
    background-color: #e98e0f;
    padding: 10px;
    border: none;
    font-weight: bold;
    color: black;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .backBtn:hover {
    background-color: #ffb733;
  }
  