/* Dashboard Page Styles */

.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../../../public/assets/pagewormu.jpg') no-repeat center center/cover;
  background-attachment: fixed;
  overflow-x: hidden;
}

.pageContent {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.title {
  color: white;
  font-size: 2rem;
  margin-bottom: 2rem;
  font-family: Quicksand, sans-serif;
}

.dashboardSection {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 2rem;
  margin-bottom: 2rem;
  color: white;
}

.dashboardSection h2 {
  color: #FFD93D;
  margin-bottom: 1.5rem;
  font-family: Quicksand, sans-serif;
}

.loadingText {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
}

.error {
  color: #ff6b6b;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 5px;
  margin: 1rem 0;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
}

.createButton {
  background-color: #FFD93D;
  color: black;
  border: none;
  padding: 0.8rem 1.5rem;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background 0.3s ease;
}

.createButton:hover {
  background-color: #ffc300;
}

.calculationsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.calculationCard {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.calculationCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.calculationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.calculationHeader h3 {
  margin: 0;
  color: #FFD93D;
}

.date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.calculationDetails {
  margin-bottom: 1.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.personalInfo, .resultsInfo {
  flex: 1;
  min-width: 180px;
}

.personalInfo p, .resultsInfo p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.macros {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.protein {
  color: #D43BF6;
}

.carbs {
  color: #47CF73;
}

.fat {
  color: #FFD93D;
}

.calculationActions {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.viewButton, .deleteButton {
  padding: 0.5rem 1rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
  flex: 1;
}

.viewButton {
  background-color: rgba(71, 207, 115, 0.2);
  color: #47CF73;
  border: 1px solid #47CF73;
}

.viewButton:hover {
  background-color: rgba(71, 207, 115, 0.3);
}

.deleteButton {
  background-color: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.deleteButton:hover {
  background-color: rgba(255, 107, 107, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pageContent {
    padding: 1rem;
  }

  .calculationsList {
    grid-template-columns: 1fr;
  }

  .calculationCard {
    padding: 1rem;
  }
}
