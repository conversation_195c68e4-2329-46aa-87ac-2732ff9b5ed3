.pageWrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-image: url('/assets/background_landing_signin.jpg');

    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    overflow-x: hidden;
  }


.page {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 100px;
  }



  .formTitle {
    font-family: Quicksand;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 10px;
  }
  .resetForm {
    background: rgba(0, 0, 0, 0.7);
    padding: 40px;
    border-radius: 10px;
    width: 90%;
    max-width: 350px;
    color: #fff;
    height: auto;
    margin-top: -80px;
  }

  .subtitle {
    font-size: 12px;
    text-align: center;
    margin-bottom: 20px;
  }

  .inputField {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    border: none;
    outline: none;
  }

  .submitBtn {
    width: 100%;
    background-color: #e98e0f;
    padding: 10px;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }

  .submitBtn:hover {
    background-color: #ffb733;
  }

  .submitBtn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .errorText {
    color: #ff6b6b;
    font-size: 14px;
    text-align: center;
    margin-bottom: 15px;
    background-color: rgba(255, 107, 107, 0.1);
    padding: 8px;
    border-radius: 4px;
  }

  .successText {
    color: #47CF73;
    font-size: 14px;
    text-align: center;
    margin-bottom: 15px;
    background-color: rgba(71, 207, 115, 0.1);
    padding: 8px;
    border-radius: 4px;
  }

  .emailInfo {
    font-size: 12px;
    color: #ccc;
    text-align: center;
    margin-bottom: 10px;
  }

  .backButtonContainer {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }

  .backButton {
    background-color: transparent;
    color: #ccc;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .backButton:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: white;
  }
