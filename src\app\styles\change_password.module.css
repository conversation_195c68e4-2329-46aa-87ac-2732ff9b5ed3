.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-image: url('/assets/background_landing_signin.jpg');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow-x: hidden;
}

.page {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 100px;
}

.passwordForm {
  background: rgba(0, 0, 0, 0.7);
  padding: 40px;
  border-radius: 10px;
  width: 90%;
  max-width: 350px;
  color: #fff;
  height: auto;
  margin-top: -50px;
}

.formTitle {
  font-family: Quicksand;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 12px;
  text-align: center;
  margin-bottom: 20px;
}

.inputGroup {
  margin-bottom: 15px;
  position: relative;
}

.inputField {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: none;
  outline: none;
}

.passwordToggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
}

.errorText {
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 5px;
  text-align: left;
}

.submitBtn {
  width: 100%;
  background-color: #e98e0f;
  padding: 10px;
  color: white;
  font-weight: bold;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 10px;
}

.submitBtn:hover {
  background-color: #ffb733;
}

.passwordRequirements {
  margin-top: 15px;
  font-size: 12px;
  color: #ccc;
}

.passwordRequirements ul {
  padding-left: 20px;
  margin-top: 5px;
}

.passwordRequirements li {
  margin-bottom: 3px;
}



/* Responsive adjustments */
@media (max-width: 480px) {
  .passwordForm {
    padding: 30px 20px;
  }
}
