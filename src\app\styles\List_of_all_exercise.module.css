.pageWrapper {
    display: flex;
    flex-direction: column;
    min-height: 150vh;
    background-image: url('/assets/pagewormu.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow-x: hidden;
  }
  
  .pageContent {
    flex: 1;
    padding: 20px;
    color: white;
  }
  
  .title {
    font-size: 24px;
    font-weight: bold;
    font-family: Quicksand;
    margin: 30px 0 40px 30px;
  }
  
  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    padding: 0 30px;
  }
  
  .card {
    text-align: center;
  }
  
  .videoWrapper {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.3);
    transition: transform 0.3s ease;
  }
  
  .videoWrapper:hover {
    transform: scale(1.03);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.7);
  }
  
  .label {
    color: orange;
    font-family: Quicksand;
    font-weight: 700;
    font-size: 18px;
    margin-top: 10px;
   
  }
  .videoWrapper {
    position: relative;
  }
  
  .clickOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    cursor: pointer;
  }
 