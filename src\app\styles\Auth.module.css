/* Authentication Pages Styles */

.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../../../public/assets/pagewormu.jpg') no-repeat center center/cover;
  background-attachment: fixed;
}

.pageContent {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.authContainer {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 2rem;
  width: 100%;
  max-width: 450px;
  color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.title {
  color: #FFD93D;
  text-align: center;
  margin-bottom: 1.5rem;
  font-family: Quicksand, sans-serif;
}

.errorMessage {
  background-color: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  padding: 0.8rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 107, 107, 0.5);
}

.successMessage {
  background-color: rgba(71, 207, 115, 0.2);
  color: #47CF73;
  padding: 0.8rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  text-align: center;
  border: 1px solid rgba(71, 207, 115, 0.5);
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.input {
  padding: 0.8rem;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #FFD93D;
}

.submitButton {
  background-color: #FFD93D;
  color: black;
  border: none;
  padding: 0.8rem;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: background 0.3s ease;
  font-size: 1rem;
}

.submitButton:hover {
  background-color: #ffc300;
}

.submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.authLinks {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.link {
  color: #FFD93D;
  text-decoration: none;
  transition: color 0.3s ease;
}

.link:hover {
  color: #ffc300;
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 500px) {
  .authContainer {
    padding: 1.5rem;
  }
  
  .pageContent {
    padding: 1rem;
  }
}
