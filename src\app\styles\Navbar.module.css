/* Navbar Styling */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 50px;
  color: #020202;
  font-family: Inter;
  font-size: 18px;
  font-weight: 800;
  background: rgba(0, 0, 0, 0.9);
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 1000;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

/* Left section containing logo and back button */
.leftSection {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logoContainer {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.logo {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 5px;
}

.navLinks {
  display: flex;
  justify-content: center;
  gap: 50px;
  flex-grow: 1;
}

.link {
  color: #fffbfb;
  font-family: Inter;
  font-size: 18px;
  font-weight: 800;
  text-decoration: none;
}

.link:hover {
  color: #e98e0f;
  cursor: pointer;
}

.signinButton {
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(233, 142, 15, 0.8);
  color: #ffffff;
  font-family: Quicksand;
  font-size: 16px;
  font-weight: 500;
  padding: 7.8px;
  width: 99px;
  height: 28.8px;
  border-radius: 3px;
  border: 2px solid #fff;
  cursor: pointer;
}

.signinButton:hover {

  background: rgba(233, 142, 15, 1);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.backButton {
  background: none;
  color: #ffaf03;
  border: none;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.backButton:hover {
  color: #e98e0f;
  background: rgba(255, 175, 3, 0.1);
}

/* User controls container */
.userControls {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Logout button */
.logoutButton {
  background: rgba(233, 142, 15, 0.8);
  color: #000;
  border: 2px solid #fff;
  border-radius: 3px;
  padding: 7.8px 15px;
  font-family: Quicksand;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.logoutButton:hover {
  background: rgba(233, 142, 15, 1);
}

/* User menu styles */
.userMenu {
  position: relative;
}

/* Profile button with picture/initials */
.profileButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
}

/* Profile picture container */
.profilePicture {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(233, 142, 15, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  transition: border-color 0.3s;
}

.profileButton:hover .profilePicture {
  border-color: rgba(233, 142, 15, 1);
}

/* Profile image */
.profileImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Profile initials (shown when no image) */
.profileInitials {
  color: #fff;
  font-family: Quicksand;
  font-size: 18px;
  font-weight: 700;
}

.dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 5px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  width: 150px;
  z-index: 10;
  overflow: hidden;
  border: 1px solid rgba(233, 142, 15, 0.5);
}

.dropdownItem {
  display: block;
  padding: 10px 15px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s;
  text-align: left;
  width: 100%;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-family: Quicksand;
}

.dropdownItem:hover {
  background-color: rgba(233, 142, 15, 0.3);
  color: white;
}