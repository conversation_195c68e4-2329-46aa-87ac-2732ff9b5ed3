/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@600;700;800&family=Quicksand:wght@400;500;600;700&display=swap');

.pageWrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-image: url('/assets/pagewormu.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    overflow-x: hidden;
  }

  .page {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: flex-start;
    padding: 50px 5%;
    gap: 50px;
    color: white;
  }

  .videoSection {
    flex: 1;
    max-width: 640px;
  }

  .video {
    width: 100%;
    height: 360px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 184, 51, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .video:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
  }

  .infoSection {
    flex: 1;
    max-width: 500px;
    background-color: rgba(0, 0, 0, 0.85);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 184, 51, 0.1); /* Subtle border with orange tint */
  }



.titleRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .title {
    font-size: 32px;
    font-family: var(--font-montserrat), 'Montserrat', sans-serif;
    font-weight: 700;
    margin-bottom: 0;
    color: #FFB833; /* Slightly warmer orange */
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  }

  .description {
    font-size: 17px;
    margin-bottom: 25px;
    font-family: var(--font-quicksand), 'Quicksand', sans-serif;
    font-weight: 500;
    line-height: 1.6;
    color: #f0f0f0; /* Slightly off-white for better eye comfort */
  }

  .details p {
    font-size: 16px;
    line-height: 1.6;
    font-family: var(--font-quicksand), 'Quicksand', sans-serif;
    margin-bottom: 12px;
    color: #f0f0f0;
  }

  .details ul {
    font-size: 16px;
    line-height: 1.6;
    font-family: var(--font-quicksand), 'Quicksand', sans-serif;
    list-style-type: disc;
    padding-left: 20px;
    margin-bottom: 20px;
    color: #f0f0f0;
  }

  .details li {
    margin-bottom: 8px;
  }

  .details strong {
    font-weight: 600;
    color: #FFB833; /* Match heading color for consistency */
  }
