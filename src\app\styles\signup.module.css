/* Full page structure with enhanced background */
.pageWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: url('/assets/background_landing_signin.jpg') no-repeat center center fixed;
  background-size: 100% 100%;
  overflow-x: hidden;
}

.page {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 100px;
  margin-bottom: 20px;
}

/* Enhanced signup form with modern styling */
.signupForm {
  background: rgba(0, 0, 0, 0.75);
  padding: 35px;
  border-radius: 20px;
  width: 90%;
  max-width: 400px;
  color: #fff;
  margin-top: -50px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 184, 51, 0.15);
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.8s ease-out;
  position: relative;
  overflow: hidden;
}

/* Add subtle gradient overlay */
.signupForm::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(233, 142, 15, 0.5), transparent);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced title styling */
.title {
  font-family: var(--font-montserrat, 'Montserrat'), sans-serif;
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
  color: #FFB833;
  letter-spacing: 0.5px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  padding-bottom: 20px;
}

/* Decorative underline for title */
.title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  border-radius: 2px;
}

/*  error and success messages */
.errorMessage {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(255, 107, 107, 0.25));
  color: var(--error);
  padding: 15px 20px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid rgba(255, 107, 107, 0.3);
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.1);
  animation: slideIn 0.3s ease-out;
}

.successMessage {
  background: linear-gradient(135deg, rgba(71, 207, 115, 0.15), rgba(71, 207, 115, 0.25));
  color: var(--success);
  padding: 15px 20px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid rgba(71, 207, 115, 0.3);
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 15px rgba(71, 207, 115, 0.1);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/*  input field styling */
.inputField {
  width: 100%;
  padding: 14px 18px;
  margin-bottom: 20px;
  border-radius: var(--border-radius-md);
  border: 2px solid transparent;
  outline: none;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: var(--transition-normal);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
}

.inputField:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(233, 142, 15, 0.2), 0 4px 20px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.inputField::placeholder {
  color: #888;
  font-weight: 400;
}

/* Enhanced password input container */
.passwordInput {
  position: relative;
  margin-bottom: 20px;
  width: 100%;
}

.passwordInput input {
  width: 100%;
  padding: 14px 50px 14px 18px;
  border-radius: var(--border-radius-md);
  border: 2px solid transparent;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  outline: none;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 16px;
  font-weight: 500;
  transition: var(--transition-normal);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
}

.passwordInput input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(233, 142, 15, 0.2), 0 4px 20px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

/* Enhanced password toggle button */
.togglePassword {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 18px;
  transition: var(--transition-fast);
  padding: 5px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.togglePassword:hover {
  background: rgba(233, 142, 15, 0.2);
  transform: translateY(-50%) scale(1.1);
}

/* Enhanced submit button with modern styling */
.submitButton {
  width: 100%;
  padding: 16px 20px;
  margin-top: 20px;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: #fff;
  font-family: var(--font-quicksand, 'Quicksand'), sans-serif;
  font-size: 18px;
  font-weight: 700;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: 0 6px 20px rgba(233, 142, 15, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gradient overlay effect */
.submitButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  z-index: -1;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.submitButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(233, 142, 15, 0.4);
}

.submitButton:hover::before {
  opacity: 1;
}

.submitButton:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(233, 142, 15, 0.3);
}

.submitButton:disabled {
  background: linear-gradient(135deg, #999, #777);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.submitButton:disabled::before {
  display: none;
}

/* Enhanced login redirect styling */
.loginRedirect {
  font-size: 14px;
  text-align: center;
  margin-top: 25px;
  color: var(--muted-text);
  font-weight: 400;
}

.loginRedirect a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-fast);
  position: relative;
}

.loginRedirect a:hover {
  color: var(--primary-light);
  text-shadow: 0 0 8px rgba(233, 142, 15, 0.3);
}

.loginRedirect a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  transition: width 0.3s ease;
}

.loginRedirect a:hover::after {
  width: 100%;
}

/* Form labels styling */
.formLabel {
  display: block;
  margin-bottom: 8px;
  color: var(--light-text);
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.3px;
}
