/* Home Page Styles */
.pageWrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../../../public/assets/pagewormu.jpg') no-repeat center center/cover;
  background-attachment: fixed;
  overflow-x: hidden;
}

.pageContent {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Welcome Section */
.welcomeSection {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  border: 1px solid rgba(233, 142, 15, 0.3);
}

.welcomeTitle {
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 700;
}

.welcomeQuote {
  color: #FFD93D;
  font-size: 1.2rem;
  font-style: italic;
  font-family: var(--font-quicksand), sans-serif;
}

/* Quick Actions */
.quickActions {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 3rem;
  justify-content: center;
}

.actionCard {
  flex: 1;
  min-width: 250px;
  max-width: 350px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(233, 142, 15, 0.3);
}

.actionCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(233, 142, 15, 0.8);
}

.actionIcon {
  margin: 0 auto 1.5rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(233, 142, 15, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(233, 142, 15, 0.3);
}

.actionTitle {
  color: #FFD93D;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 700;
}

.actionDescription {
  color: #fff;
  font-size: 1rem;
  font-family: var(--font-quicksand), sans-serif;
}

/* Stats Section */
.statsSection {
  margin-bottom: 3rem;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 2rem;
  border: 1px solid rgba(233, 142, 15, 0.3);
}

.sectionTitle {
  color: #fff;
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: #e98e0f;
  border-radius: 2px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.sectionHeader .sectionTitle {
  margin-bottom: 0;
}

.viewAllLink {
  color: #e98e0f;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border: 1px solid #e98e0f;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-family: var(--font-montserrat), sans-serif;
}

.viewAllLink:hover {
  background-color: #e98e0f;
  color: #000;
  transform: translateY(-1px);
}

.statsGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: space-around;
}

.statCard {
  flex: 1;
  min-width: 150px;
  max-width: 200px;
  text-align: center;
  padding: 1.5rem;
  background-color: rgba(20, 20, 20, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(233, 142, 15, 0.2);
}

.statTitle {
  color: #FFD93D;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 600;
}

.statValue {
  color: #fff;
  font-size: 2rem;
  font-family: var(--font-quicksand), sans-serif;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.statSubtext {
  color: #b0b0b0;
  font-size: 0.9rem;
  font-family: var(--font-quicksand), sans-serif;
  margin-top: 0;
}

/* Recent Activity */
.recentActivity {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 2rem;
  border: 1px solid rgba(233, 142, 15, 0.3);
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activityItem {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.8rem;
  background-color: rgba(20, 20, 20, 0.6);
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.activityItem:hover {
  background-color: rgba(233, 142, 15, 0.1);
}

.activityIcon {
  color: #e98e0f;
  font-size: 1.5rem;
  margin-top: 0.2rem;
}

.activityContent {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.activityText {
  color: #fff;
  font-size: 1rem;
  font-family: var(--font-quicksand), sans-serif;
  margin-bottom: 0.5rem;
}

.activityDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-top: 0.3rem;
}

.activityDetails span {
  color: #b0b0b0;
  font-size: 0.85rem;
  background-color: rgba(20, 20, 20, 0.8);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  border: 1px solid rgba(233, 142, 15, 0.2);
}

.activityDetails strong {
  color: #FFD93D;
}

.emptyState {
  color: #b0b0b0;
  text-align: center;
  padding: 2rem;
  font-style: italic;
}

.emptyState p {
  margin: 0.5rem 0;
}

.loadingState {
  color: #b0b0b0;
  text-align: center;
  padding: 2rem;
  font-style: italic;
}

.activityDate {
  color: #888;
  font-size: 0.8rem;
  margin-left: auto;
}

.activityDescription {
  color: #b0b0b0;
  font-size: 0.85rem;
  margin: 0.3rem 0;
  font-style: italic;
}

.activityContent {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 0.2rem;
}

.activityText {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workoutNotes {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(40, 40, 40, 0.6);
  border-radius: 4px;
  border-left: 3px solid #e98e0f;
  color: #e0e0e0;
  font-size: 0.9rem;
  font-style: italic;
}

/* Quick Actions */
.quickActions {
  margin-top: 2rem;
  text-align: center;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.logWorkoutButton {
  background: linear-gradient(135deg, #e98e0f, #d17a00);
  color: #000;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 700;
  font-family: var(--font-montserrat), sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(233, 142, 15, 0.3);
  text-decoration: none;
}

.logWorkoutButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(233, 142, 15, 0.4);
  background: linear-gradient(135deg, #d17a00, #b86800);
}

.historyButton {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 700;
  font-family: var(--font-montserrat), sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
  text-decoration: none;
  display: inline-block;
}

.historyButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
  background: linear-gradient(135deg, #138496, #0f6674);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .pageContent {
    padding: 1rem;
  }

  .welcomeTitle {
    font-size: 1.8rem;
  }

  .actionCard {
    min-width: 100%;
  }

  .statCard {
    min-width: 100%;
  }
}
